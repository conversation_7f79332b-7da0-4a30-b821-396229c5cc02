<template>
  <div id="app" class="app-container">
    <el-drawer
        :withHeader="false"
        :visible.sync="isOpen"
        :before-close="handleClose"
        direction="ttb"
        size="100%"
        :show-close="false">
      <div class="main">
        <!-- 左侧导航栏 -->
        <div class="left-side">
          <div class="cate">
            <ul class="nav">
              <li class="nav-item" v-for="(menu, index) in menuList" :key="index">
                <a :class="'nav-link' + (activeMenu == menu.key ? ' active' : '')" href="javascript:;" @click="switchMenu(menu.key)">
                  <img class="cate-logo" :src="menu.logo">
                  <span>{{ menu.name }}</span>
                </a>
              </li>
              <li class="nav-item logout-item">
                <a class="nav-link logout-link" href="javascript:;" @click="logout">
                  <i class="el-icon-d-arrow-left cate-logo logout-icon"></i>
                  <span>退出</span>
                </a>
              </li>
            </ul>
          </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="content-wrapper" v-if="activeMenu == 'cashier'">
          <!-- 顶部导航栏 -->
          <div class="top-navbar">
            <div class="navbar-content">
              <div class="navbar-title">
                <div class="welcome-text" v-if="storeInfo">
                  <span>{{ storeInfo.name }}(收银台)</span>
                  <!--                  <span class="account">     您好，{{ accountInfo.realName ? accountInfo.realName : accountInfo.accountName }}！</span>-->
                </div>
              </div>
            </div>
          </div>

          <div class="main-content">
            <!-- 左侧商品内容区域 -->
            <div class="goods-container">
              <!-- 搜索栏和分类标签 -->
              <div class="title">
                <el-form class="search-form" ref="searchForm" :inline="true" :model="searchForm">
                  <el-form-item class="form-item" style="margin-right: 20px; margin-bottom: 0; flex: 1 1 auto;" label="" prop="keyword">
                    <el-input v-model="searchForm.keyword" prefix-icon="el-icon-search" @keyup.enter.native="doQueryGoods" class="input-item" placeholder="请输入商品关键字：商品名称、条码、商品ID..." clearable maxlength="100" />
                  </el-form-item>
                  <el-button class="search-goods" @click="doQueryGoods()" icon="el-icon-search">查询商品</el-button>
                  <el-form-item class="setting">
                    <span :class="goodsMode == 'small' ? 'item active' : 'item'" @click="switchGoodMode('small')"><svg t="1741317847001" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6842" width="16" height="16"><path d="M263.104 760.896V960H64v-199.104h199.104z m348.48 0V960H412.416v-199.104h199.168z m348.416 0V960h-199.104v-199.104H960zM263.104 412.416v199.168H64V412.416h199.104z m348.48 0v199.168H412.416V412.416h199.168z m348.416 0v199.168h-199.104V412.416H960zM263.104 64v199.104H64V64h199.104z m348.48 0v199.104H412.416V64h199.168zM960 64v199.104h-199.104V64H960z" p-id="6843"></path></svg><span class="text">小图</span></span>
                    <span :class="goodsMode == 'big' ? 'item active' : 'item'" @click="switchGoodMode('big')"><svg t="1741318028632" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2278" width="16" height="16"><path d="M934.4 934.4V633.6H633.6v300.8h300.8m-544 0V633.6H89.6v300.8h300.8m544-544V89.6H633.6v300.8h300.8m-544 0V89.6H89.6v300.8h300.8M960 1024H608a64 64 0 0 1-64-64V608a64 64 0 0 1 64-64H960a64 64 0 0 1 64 64V960a64 64 0 0 1-64 64z m-544 0H64a64 64 0 0 1-64-64V608a64 64 0 0 1 64-64h352a64 64 0 0 1 64 64V960a64 64 0 0 1-64 64zM960 480H608a64 64 0 0 1-64-64V64a64 64 0 0 1 64-64H960a64 64 0 0 1 64 64v352a64 64 0 0 1-64 64z m-544 0H64a64 64 0 0 1-64-64V64a64 64 0 0 1 64-64h352a64 64 0 0 1 64 64v352a64 64 0 0 1-64 64z" fill="#333333" p-id="2279"></path></svg><span class="text">大图</span></span>
                  </el-form-item>
                </el-form>
                <!-- 分类标签 -->
                <el-tabs class="tab-box" type="card" v-model="navTab" @tab-click="switchTab">
                  <el-tab-pane label="全部" name="0"></el-tab-pane>
                  <el-tab-pane v-for="tab in tabList" :label="tab.name" :key="tab.id+''" :name="tab.id+''"></el-tab-pane>
                </el-tabs>
              </div>

              <!-- 商品列表 -->
              <div class="goods-list-container">
                <div :class="'goods-list-' + goodsMode">
                  <div class="goods-item" v-for="(goodsInfo, index) in goodsList" :key="index">
                    <div
                      :class="['item', { 'out-of-stock': isOutOfStock(goodsInfo) }]"
                      @click="!isOutOfStock(goodsInfo) && clickGoods(goodsInfo)"
                      :style="{ cursor: isOutOfStock(goodsInfo) ? 'not-allowed' : 'pointer' }"
                    >
                      <div class="image-container">
                        <img class="image" lazy :src="imagePath + goodsInfo.logo">
                        <div v-if="isOutOfStock(goodsInfo)" class="sold-out-overlay">
                          <span class="sold-out-text">商品已售完</span>
                        </div>
                      </div>
                      <div class="goods-name">{{ goodsInfo.name }}</div>
                      <div class="goods-price">￥{{ goodsInfo.price }}</div>
                    </div>
                  </div>

                  <el-empty v-if="goodsList.length == 0" description="暂无商品..." :image-size="100"></el-empty>
                </div>
              </div>
            </div>

            <!-- 右侧购物车面板 -->
            <div class="cart-container">
              <!-- 用户信息 -->
              <div class="cart-user-info">
                <div class="user-info">
                  <div class="user-header">
                    <span class="user-name">{{ memberInfo ? memberInfo.name : '普通用户' }}</span>
                    <span class="user-amount">¥{{ memberInfo && memberInfo.balance ? memberInfo.balance.toFixed(2) : '0.00' }}</span>
                  </div>
                  <div class="user-details">
                    <div class="detail-row" style="display: flex; justify-content: space-between;margin-top: 30px;">
                      <span class="phone-number">手机号：{{ memberInfo && memberInfo.mobile ? memberInfo.mobile : '无' }}</span>
                      <!-- <span class="balance" style="margin-left: 70px;">{{ memberInfo && memberInfo.balance ? memberInfo.balance.toFixed(2) : '0.00' }} 元</span> -->
                      <!-- 积分展示 -->
                      <span class="balance" style="margin-left: 70px;">{{ memberInfo && memberInfo.point ? memberInfo.point : '0' }} 积分</span>
                    </div>
                    <div class="tag-row" style="margin-top: 10px;font-size: 12px;">
                      <span class="member-tag" v-if="memberInfo && memberInfo.gradeId">{{ memberGradeName || '会员' }}</span>
                    </div>
                    <div class="member-card" style="margin-top: 10px;font-size: 12px;">
                      <span class="member-card-number">会员卡号：{{ memberInfo ? memberInfo.userNo : '未绑定卡号' }}</span>
                    </div>
                  </div>
                  <el-button size="mini" class="switch-btn" type="primary" icon="el-icon-refresh" @click="switchMember()">关联会员</el-button>
                </div>
              </div>

              <!-- 购物车商品列表 -->
              <div class="carts">
                <div>
                  <div class="tab">
                    <div class="cart-list" v-if="cartList.length > 0">
                      <div class="cart-item" v-for="(cartInfo, index) in cartList" :key="index">
                        <img class="image" :src="cartInfo.logo"/>
                        <div class="info">
                          <div class="name">{{ cartInfo.name }}</div>
                          <div class="spec" v-if="cartInfo.specList && cartInfo.specList.length > 0">
                            <span class="item" v-for="(spec, i) in cartInfo.specList" :key="i" :title="spec.value">{{ spec.value }}</span>
                          </div>
                          <div class="num">
                            <el-input-number class="input" @change="changeBuyNum(cartInfo)" v-model="cartInfo.buyNum" :min="1" :max="1000" size="small"/>
                          </div>
                        </div>
                        <div class="option">
                          <div class="remove el-icon-delete" @click="removeFromCart(cartInfo.cartId)"></div>
                          <div class="total">￥{{ (cartInfo.price * cartInfo.buyNum).toFixed(2) }}</div>
                        </div>
                      </div>
                    </div>
                    <div class="empty" v-if="cartList.length < 1">
                      <el-empty description="暂无结算商品" :image-size="40"></el-empty>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 购物车底部 -->
              <div class="cart-footer">
                <div class="cart-total">
                  <div class="total-row">
                    <div>总件数：<b class="num">{{ cartTotalNum }}</b></div>
                  </div>
                  <div class="total-row">
                    <div>总金额：<b class="price">￥{{ cartTotalPrice ? cartTotalPrice.toFixed(2) : '0.00' }}</b></div>
                  </div>
                </div>
                <div class="cart-buttons">
                  <button class="cart-button secondary" @click="hangUp()">挂单 / 取单</button>
                  <button class="cart-button primary" v-if="cartTotalNum > 0" @click="doSettlement()">提交结算</button>
                  <button class="cart-button primary" v-if="cartTotalNum == 0" @click="doCashier()">无商品收款</button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 订单列表组件 start-->
        <orderList v-if="activeMenu == 'order'" @doPayOrder="doPayOrder"></orderList>
        <!-- 订单列表组件 end-->

        <!-- 会员列表组件 start-->
        <memberList v-if="activeMenu == 'member'"></memberList>
        <!-- 会员列表组件 end-->

        <!-- 会员卡券组件 start-->
        <userCoupon v-if="activeMenu == 'userCoupon' && !confirmCoupon" @doConfirmCoupon="doConfirmCoupon"></userCoupon>
        <!-- 会员卡券组件 end-->

        <!-- 卡券核销组件 start-->
        <couponConfirm v-if="confirmCoupon" :couponCode="couponCode" @doUserCoupon="doUserCoupon"></couponConfirm>
        <!-- 卡券核销组件 end-->
      </div>
    </el-drawer>

    <!-- 规格详情 start-->
    <el-dialog title="选择商品规格" :visible.sync="openGoodsDialog" class="common-dialog" append-to-body>
      <div class="goods-info">
        <div class="name">{{ goodsInfo.name }}</div>
        <div class="price">￥{{ goodsInfo.price }}</div>
        <div class="num"><el-input-number class="input" v-model="goodsNum" :min="1" :max="1000"/></div>
        <div class="spec-list" v-if="goodsInfo.isSingleSpec == 'N'">
          <div class="spec-item" v-for="(specInfo, index) in goodsInfo.specList" :key="index">
            <div class="spec-name">{{ specInfo.name }}</div>
            <div class="values">
              <span v-for="(value, i) in specInfo.child" :key="i" :class="goodsSpecIds.includes(value.id) ? 'value active' : 'value'" @click="selectGoodsSpec(value.id)">{{ value.name }}</span>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" class="main-button" @click="addToCart()" style="background: #00afff">加入结算</el-button>
        <el-button @click="closeGoodsDialog()">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 规格详情 end-->

    <!--关联会员对话框 start-->
    <switchMemberDialog :show-dialog="openSwitchMemberDialog" @doSwitchMember="doSwitchMember"/>
    <!--关联会员对话框 end-->

    <!--关联员工对话框 start-->
    <bindStaffDialog :show-dialog="openBindStaffDialog" @doBindStaff="doBindStaff" @closeDialog="closeDialog"/>
    <!--关联员工对话框 end-->

    <!--结算对话框 start-->
    <settlementDialog :show-dialog="openSettlementDialog" :memberInfo="memberInfo" :staffInfo="staffInfo" :totalPrice="cartTotalPrice" :remarks="cartRemark" :orderInfo="orderInfo" :couponList="couponList" @submit="submitSettlement" @switchMember="switchMember" @bindStaff="bindStaff" @closeDialog="closeDialog"/>
    <!--结算对话框 end-->

    <!--扫码付款对话框 start-->
    <scanPayCodeDialog ref="scanPayCodeDialog" :show-dialog="openScanPayCodeDialog" :memberInfo="memberInfo" :orderId="orderId" :payType="payType" :payAmount="payAmount" @closeDialog="closeDialog" @showPayResult="showPayResult"/>
    <!--扫码付款对话框 end-->

    <!--支付结果对话框 start-->
    <payResultDialog :show-dialog="openPayResultDialog" :payResult="payResult" @showOrderPrint="showOrderPrint" @closeDialog="closeDialog"/>
    <!--支付结果对话框 end-->

    <!--打印订单对话框 start-->
    <orderPrintDialog :show-dialog="openOrderPrintDialog" :storeInfo="storeInfo" :orderInfo="orderInfo" @closeDialog="closeDialog"/>
    <!--打印订单对话框 end-->

    <!--挂单对话框 start-->
    <hangUpDialog :show-dialog="openHangUpDialog" :memberInfo="memberInfo" :cartList="cartList" @getHangNo="getHangNo" @doHangUp="doHangUp" @closeDialog="closeDialog"/>
    <!--挂单对话框 end-->

    <!-- 无商品收款组件 start-->
    <noGoodsCashier :show-dialog="openNoGoodsCashierDialog" @submit="submitCashier" @closeDialog="closeDialog"></noGoodsCashier>
    <!-- 无商品收款组件 end-->
  </div>
</template>

<script>
import { init, getGoodsInfo, searchGoods, getCartList, saveCart, removeFromCart, submitSettlement, doPay, getMemberInfoById } from "@/api/cashier";
import { getOrderInfo } from "@/api/order";
import { getUserGradeInfo } from "@/api/member";
import { getUserId, setUserId, removeUserId } from '@/utils/auth';
const { ipcRenderer } = require('electron');
import switchMemberDialog from "./components/switchMemberDialog";
import bindStaffDialog from "./components/bindStaffDialog";
import settlementDialog from "./components/settlementDialog";
import scanPayCodeDialog from "./components/scanPayCodeDialog";
import payResultDialog from "./components/payResultDialog";
import orderPrintDialog from "./components/orderPrintDialog";
import hangUpDialog from "./components/hangUpDialog";
import orderList from "./components/orderList";
import memberList from "./components/memberList";
import userCoupon from "./components/userCoupon";
import couponConfirm from "./components/couponConfirm";
import noGoodsCashier from "./components/noGoodsCashier";

export default {
  name: "Cashier",
  components: {
    switchMemberDialog,
    settlementDialog,
    scanPayCodeDialog,
    payResultDialog,
    orderPrintDialog,
    hangUpDialog,
    orderList,
    memberList,
    userCoupon,
    couponConfirm,
    noGoodsCashier,
    bindStaffDialog
  },
  data() {
    return {
      // 系统名称
      systemName: process.env.VUE_APP_TITLE,
      // 导航tab
      navTab: '0',
      isOpen: true,
      page: 1,
      pageSize: 1000,
      totalGoods: 0,
      openGoodsDialog: false,
      openSwitchMemberDialog: false,
      openBindStaffDialog: false,
      openSettlementDialog: false,
      openNoGoodsCashierDialog: false,
      openScanPayCodeDialog: false,
      openPayResultDialog: false,
      openOrderPrintDialog: false,
      openHangUpDialog: false,
      searchForm: { keyword: '' },
      payResult: { isSuccess: false, payAmount: 0, orderId: 0 },
      goodsForm: {},
      // 左侧菜单
      menuList: [{ name: '收银', key: 'cashier', logo: require('../../assets/images/cashier.png') },
        { name: '订单', key: 'order', logo: require('../../assets/images/order.png') },
        { name: '会员管理', key: 'member', logo: require('../../assets/images/hot.png') },
        { name: '卡券核销', key: 'userCoupon', logo: require('../../assets/images/life.png') }
      ],
      // 激活菜单
      activeMenu: 'cashier',
      // 导航栏tab
      tabList: [],
      // 当前操作会员
      memberInfo: null,
      // 会员等级名称
      memberGradeName: '',
      // 绑定的员工
      staffInfo: null,
      // 当前登录用户
      accountInfo: {},
      // 当前门店信息
      storeInfo: {},
      // 当前操作商品
      goodsInfo: { num : 1, specList : [], skuList: [] },
      // 当前选择属性
      goodsSpecIds: [],
      // 商品数量
      goodsNum: 1,
      // 当前分类
      cateId: 0,
      // 图片目录
      imagePath: '',
      // 商品分类列表
      cateList: [],
      // 商品列表
      goodsList: [],
      // 购物车列表
      cartList: [],
      // 订单列表
      orderList: [],
      // 总金额
      cartTotalPrice: 0,
      // 购物车备注
      cartRemark: '',
      // 总件数
      cartTotalNum: 0,
      // 支付金额
      payAmount: 0,
      // 当前订单号
      orderId: 0,
      // 支付方式
      payType: '',
      // 当前订单
      orderInfo: {},
      // 可用卡券列表
      couponList: [],
      // 挂单序号
      hangNo: '',
      isSearch: false,
      // 核销卡券码
      couponCode: '',
      // 是否核销卡券
      confirmCoupon: false,
      // 商品模式
      goodsMode: this.$cache.local.get("goodsMode") ? this.$cache.local.get("goodsMode") : 'small',
      // 防重复处理标志
      isProcessingBarcode: false,
      // 最后处理的条码和时间，用于防重复
      lastProcessedBarcode: '',
      lastProcessedTime: 0,
      // 事件监听器绑定标志
      keyboardListenerBound: false,
      serialListenerBound: false
    };
  },
  mounted() {
    const app = this;

    // 确保只绑定一次事件监听器
    if (app.keyboardListenerBound) {
      return;
    }
    app.keyboardListenerBound = true;

    // 监听扫码枪按键
    let code = '';
    let lastTime, nextTime; // 上次时间、最新时间
    let lastCode, nextCode; // 上次按键、最新按键
    document.onkeypress = (e) => {
      // 获取按键
      if (window.event) { // IE
        nextCode = e.keyCode;
      } else if (e.which) { // Netscape/Firefox/Opera
        nextCode = e.which;
      }
      // 如果触发了回车事件(扫码结束时间)
      if (nextCode === 13) {
        if (code.length < 3) {
          code = '';
          return;
        }

        console.log('键盘扫码事件触发:', code);

        // 特殊对话框处理
        if (app.openScanPayCodeDialog == true) {
          app.$refs.scanPayCodeDialog.submit(code);
          code = '';
          return false;
        }
        if (app.openSwitchMemberDialog == true) {
          code = '';
          return false;
        }
        if (app.openSettlementDialog == true) {
          app.$alert("请点击确定收款！");
          code = '';
          return false;
        }

        // 使用统一的条码处理方法
        app.processBarcodeUnified(code, 'keyboard');

        code = '';
        lastCode = '';
        lastTime = '';

        return true;
      }
      nextTime = new Date().getTime(); // 记录最新时间
      if (!lastTime && !lastCode) { // 如果上次时间和上次按键为空
        code += e.key; // 执行叠加操作
      }
      // 如果有上次时间及上次按键
      if (lastCode && lastTime && nextTime - lastTime > 30) {
        code = e.key;
      } else if (lastCode && lastTime) {
        code += e.key;
      }
      lastCode = nextCode;
      lastTime = nextTime;
      return true;
    }
    
    // 监听来自串口的条码数据（确保只绑定一次）
    if (!app.serialListenerBound) {
      app.serialListenerBound = true;
      ipcRenderer.on('barcode-from-serial', (event, barcode) => {
        console.log('串口扫码事件触发:', barcode);

        // 使用统一的条码处理方法
        app.processBarcodeUnified(barcode, 'serial');
      });
    }
  },
  created() {
    this.initCashier();
    this.getCartList();
  },
  beforeDestroy() {
    // 清理事件监听器
    if (this.serialListenerBound) {
      ipcRenderer.removeAllListeners('barcode-from-serial');
    }
    // 清理键盘事件
    document.onkeypress = null;
  },
  methods: {
    // 判断商品是否缺货
    isOutOfStock(goodsInfo) {
      // 检查商品库存字段
      const stock = goodsInfo.stock;
      console.log(`商品 ${goodsInfo.name} 库存检查: stock=${stock}, 是否缺货=${stock !== undefined && stock <= 0}`);
      return stock !== undefined && stock <= 0;
    },

    // 统一的条码处理方法，防止重复处理
    processBarcodeUnified(barcode, source = 'unknown') {
      const app = this;
      const currentTime = Date.now();

      console.log(`[${source}] 收到条码: ${barcode}, 时间: ${currentTime}, 处理状态: ${app.isProcessingBarcode}`);

      // 检查条码有效性
      if (!barcode || barcode.length < 3) {
        console.log('条码无效，忽略处理');
        return false;
      }

      // 检查是否是重复的条码（2秒内相同条码视为重复）
      if (app.lastProcessedBarcode === barcode &&
          currentTime - app.lastProcessedTime < 2000) {
        console.log(`检测到重复条码，忽略处理。上次处理时间: ${app.lastProcessedTime}, 间隔: ${currentTime - app.lastProcessedTime}ms`);
        return false;
      }

      // 检查是否正在处理其他条码
      if (app.isProcessingBarcode) {
        console.log('正在处理其他条码，忽略当前条码');
        return false;
      }

      // 检查是否正在加载
      if (app.loading) {
        console.log('系统正在加载，忽略条码');
        return false;
      }

      // 确保当前处于收银页面
      if (app.activeMenu !== 'cashier') {
        console.log('不在收银页面，忽略条码');
        return false;
      }

      // 确保不在其他对话框中
      if (app.openScanPayCodeDialog || app.openSwitchMemberDialog || app.openSettlementDialog) {
        console.log('当前有弹窗打开，忽略条码');
        return false;
      }

      console.log(`开始处理条码: ${barcode}`);

      // 记录当前处理的条码和时间
      app.lastProcessedBarcode = barcode;
      app.lastProcessedTime = currentTime;
      app.isProcessingBarcode = true;

      // 更新搜索框内容
      app.searchForm.keyword = barcode;

      // 使用条码添加商品到购物车
      app.addToCart(barcode);

      return true;
    },

    // 初始化数据
    initCashier() {
      const app = this;
      const userId = getUserId() > 0 ? getUserId() : 0;
      init(userId, app.cateId, 1, app.pageSize).then( response => {
            app.cateList = response.data.cateList;
            app.tabList = response.data.cateList;
            app.goodsList = response.data.goodsList;
            app.imagePath = response.data.imagePath;
            app.storeInfo = response.data.storeInfo;
            app.accountInfo = response.data.accountInfo;
            app.memberInfo = response.data.memberInfo;
            app.totalGoods = response.data.totalGoods;
            app.loading = false;
            app.hangNo = '';
            app.staffInfo = null;
            // 如果有会员信息，获取会员等级名称
            if (app.memberInfo && app.memberInfo.gradeId) {
              app.getMemberGradeName(app.memberInfo.gradeId);
            }
          }
      ).catch((err) => {
        app.loading = false;
        console.log(err.toString());
      });
    },
    // 菜单切换
    switchMenu(menuKey) {
      this.activeMenu = menuKey;
      this.confirmCoupon = false;
      return menuKey;
    },
    // tab切换
    switchTab(el) {
      this.navTab = el.name;
      this.filterCate(this.navTab);
    },
    // 过滤分类商品
    filterCate(cateId) {
      this.cateId = cateId;
      this.initCashier();
    },
    // 购物车列表
    getCartList(cartIds) {
      const app = this;
      if (app.loading) {
        return false;
      }
      app.loading = true;
      app.cartList = [];
      const switchCartIds = cartIds ? cartIds.join(",") : "";
      getCartList({ userId: getUserId(), hangNo: app.hangNo, cartIds: switchCartIds }).then( response => {
        const cartList = response.data.list;
        if (cartList && cartList.length > 0) {
          cartList.forEach(function(item) {
            const specList = [];
            if (item.specList && item.specList.length > 0) {
              item.specList.forEach(function (spec) {
                specList.push({name: spec.specName, value: spec.specValue});
              })
            }
            // 确保 buyNum 是数字类型
            const num = parseInt(item.num) || 1;
            const cartInfo = { 
              cartId: item.id, 
              skuId: item.skuId, 
              goodsId: item.goodsInfo.id, 
              name: item.goodsInfo.name, 
              logo: item.goodsInfo.logo, 
              price: item.goodsInfo.price, 
              buyNum: num, 
              specList: specList 
            };
            app.cartList.push(cartInfo);
          })
        }
        app.cartTotalPrice = response.data.payPrice;
        app.cartTotalNum = response.data.totalNum;
        app.couponList = response.data.couponList;
        app.loading = false;
      }).catch((err) => {
        app.loading = false;
        console.log(err.toString());
      });
    },
    // 查询商品
    doQueryGoods() {
      const app = this;
      if (!app.searchForm.keyword) {
        app.initCashier();
        return false;
      }
      app.loading = true;
      searchGoods({ keyword: app.searchForm.keyword }).then( response => {
        app.loading = false;
        if (response.data && response.data.length > 0) {
          app.goodsList = response.data;
        } else {
          app.$alert("抱歉，未查询到商品信息！");
          return false;
        }
      }).catch((err) => {
        app.loading = false;
        console.log(err.toString());
      });
    },
    // 点击商品规格弹框
    clickGoods(goodsInfo) {
      const app = this;

      // 检查商品是否缺货
      if (app.isOutOfStock(goodsInfo)) {
        app.$message.warning('该商品已售完，无法添加到购物车');
        return false;
      }

      if (app.loading) {
        return false;
      }
      app.loading = true;
      getGoodsInfo(goodsInfo.id).then( response => {
        app.goodsInfo = response.data.goodsInfo;
        app.goodsInfo.specList = response.data.specList;
        app.goodsInfo.skuList = response.data.skuList;
        app.goodsNum = 1;
        app.loading = false;
        if (app.goodsInfo.isSingleSpec == 'N') {
          app.openGoodsDialog = true;
        } else {
          app.addToCart(false);
        }
      }).catch((err) => {
        app.loading = false;
        console.log(err.toString());
      });
    },
    // 关闭规格弹框
    closeGoodsDialog() {
      this.openGoodsDialog = false;
      // 重置商品数量为1
      this.goodsNum = 1;
      // 重置处理标志和记录
      this.isProcessingBarcode = false;
      this.lastProcessedBarcode = '';
    },
    // 选择商品属性
    selectGoodsSpec(specId) {
      const app = this;
      let specIds = [];
      app.goodsInfo.specList.forEach(function() {
        specIds.push(0);
      })
      app.goodsInfo.specList.forEach(function(specItem, index) {
        const children = [];
        specItem.child.forEach(function(child) {
          children.push(child.id)
        })
        if (children.includes(specId)) {
          specIds[index] = specId
        } else {
          specIds[index] = app.goodsSpecIds[index] == undefined ? 0 : app.goodsSpecIds[index];
        }
      })
      app.goodsSpecIds = specIds;
    },
    // 扫码专用添加到购物车方法
    addToCartByBarcode(goodsInfo) {
      const app = this;

      // 检查商品是否缺货
      if (app.isOutOfStock(goodsInfo)) {
        app.$message.warning(`商品 "${goodsInfo.name}" 已售完，无法添加到购物车`);
        app.isProcessingBarcode = false;
        app.lastProcessedBarcode = '';
        return false;
      }

      if (app.loading) {
        console.log('addToCartByBarcode: 正在加载中，退出');
        return false;
      }

      console.log(`addToCartByBarcode开始: 商品ID=${goodsInfo.id}, 商品名=${goodsInfo.name}, 时间=${Date.now()}`);

      // 获取商品详细信息
      app.loading = true;
      getGoodsInfo(goodsInfo.id).then(response => {
        const detailGoodsInfo = response.data.goodsInfo;
        const specList = response.data.specList;
        const skuList = response.data.skuList;

        let skuId = 0;

        // 如果是单规格商品，直接添加
        if (detailGoodsInfo.isSingleSpec === 'Y') {
          if (skuList && skuList.length > 0) {
            skuId = skuList[0].id;
          }

          // 直接添加到购物车，数量固定为1
          const cartInfo = {
            goodsId: detailGoodsInfo.id,
            name: detailGoodsInfo.name,
            logo: detailGoodsInfo.logo,
            price: detailGoodsInfo.price,
            skuId: skuId,
            userId: getUserId(),
            hangNo: app.hangNo,
            buyNum: 1  // 扫码添加固定数量为1
          };

          console.log(`发起saveCart请求: 商品=${detailGoodsInfo.name}, 数量=1, 时间=${Date.now()}`);
          saveCart(cartInfo).then(response => {
            console.log(`saveCart响应: 商品=${detailGoodsInfo.name}, cartId=${response.data.cartId}, 时间=${Date.now()}`);
            app.loading = false;
            if (response.data.cartId) {
              app.getCartList();

              // 显示添加成功提示
              app.$message({
                message: `${detailGoodsInfo.name} 已添加到购物车 (数量: 1)`,
                type: 'success',
                duration: 2000
              });
            }
            // 重置处理标志和记录
            app.isProcessingBarcode = false;
            // 延迟清除条码记录，防止短时间内的重复
            setTimeout(() => {
              app.lastProcessedBarcode = '';
              console.log('条码记录已清除，可以处理新的扫码');
            }, 2000);
          }).catch((err) => {
            console.error('saveCart失败:', err);
            app.loading = false;
            app.$message.error('添加到购物车失败，请重试');
            // 重置处理标志
            app.isProcessingBarcode = false;
            app.lastProcessedBarcode = '';
          });
        } else {
          // 多规格商品，弹出规格选择对话框
          app.loading = false;
          app.goodsInfo = detailGoodsInfo;
          app.goodsInfo.specList = specList;
          app.goodsInfo.skuList = skuList;
          app.goodsNum = 1; // 确保数量为1
          app.openGoodsDialog = true;

          app.$message({
            message: `请选择 ${detailGoodsInfo.name} 的规格`,
            type: 'info',
            duration: 2000
          });

          // 重置处理标志（多规格商品需要用户手动选择，所以可以立即重置）
          app.isProcessingBarcode = false;
          app.lastProcessedBarcode = '';
        }
      }).catch((err) => {
        app.loading = false;
        console.error('获取商品详情失败:', err);
        app.$message.error('获取商品详情失败，请重试');
        // 重置处理标志
        app.isProcessingBarcode = false;
        app.lastProcessedBarcode = '';
      });
    },

    // 加入购物车
    addToCart(skuNo) {
      const app = this;
      app.isSearch = false;

      // 扫码枪扫描商品条码，直接加入购物车
      if (skuNo) {
        console.log(`addToCart被调用，条码: ${skuNo}, 处理状态: ${app.isProcessingBarcode}, 加载状态: ${app.loading}`);

        // 这里不需要额外的检查，因为统一方法已经做了所有检查
        // 只需要确保不在加载状态
        if (app.loading) {
          console.log('正在加载中，重置处理标志');
          app.isProcessingBarcode = false;
          return;
        }

        // 播放提示音
        try {
          const audioContext = new (window.AudioContext || window.webkitAudioContext)();
          const oscillator = audioContext.createOscillator();
          const gainNode = audioContext.createGain();

          oscillator.connect(gainNode);
          gainNode.connect(audioContext.destination);

          oscillator.frequency.value = 800;
          gainNode.gain.value = 0.1;

          oscillator.start();
          oscillator.stop(audioContext.currentTime + 0.1);
        } catch (error) {
          console.log('音效播放失败:', error);
        }

        app.loading = true;
        const requestTime = Date.now();
        console.log(`发起商品查询请求: ${skuNo}, 请求时间: ${requestTime}`);

        searchGoods({ keyword: skuNo }).then( response => {
          console.log(`商品查询响应: ${skuNo}, 响应时间: ${Date.now()}, 结果数量: ${response.data ? response.data.length : 0}`);
          app.loading = false;

          if (response.data && response.data.length == 1) {
            // 找到唯一商品，使用专门的扫码添加方法
            console.log(`找到唯一商品，开始添加: ${response.data[0].name}`);
            app.addToCartByBarcode(response.data[0]);
            app.isSearch = true;
          } else if (response.data && response.data.length > 1) {
            // 找到多个商品，显示在商品列表中
            app.goodsList = response.data;
            app.$message({
              message: `找到 ${response.data.length} 个相关商品，请在列表中选择`,
              type: 'warning',
              duration: 3000
            });
            // 重置处理标志
            app.isProcessingBarcode = false;
            app.lastProcessedBarcode = '';
          } else {
            app.$alert(`未找到条码为 "${skuNo}" 的商品，请检查条码是否正确。`);
            // 重置处理标志
            app.isProcessingBarcode = false;
            app.lastProcessedBarcode = '';
          }
        }).catch((err) => {
          console.error('商品查询失败:', err);
          app.loading = false;
          app.$message.error('查询商品失败，请重试');
          // 重置处理标志
          app.isProcessingBarcode = false;
          app.lastProcessedBarcode = '';
        });
        return;
      }

      if (app.loading || app.isSearch || !app.goodsInfo.id || app.goodsNum <= 0) {
        return false;
      }

      const specIds = app.goodsSpecIds.join('-');
      let skuId = 0;
      app.goodsInfo.skuList.forEach(function(skuInfo) {
        if (skuInfo.specIds == specIds) {
          skuId = skuInfo.id;
        }
      })

      if (app.goodsInfo.isSingleSpec == 'N' && skuId <= 0) {
        app.$alert("请先确认商品规格！");
        return false;
      }

      // 添加到购物车
      const cartInfo = {
        goodsId: app.goodsInfo.id,
        name: app.goodsInfo.name,
        logo: app.goodsInfo.logo,
        price: app.goodsInfo.price,
        skuId: skuId,
        userId: getUserId(),
        hangNo: app.hangNo,
        buyNum: app.goodsNum
      };

      app.loading = true;
      saveCart(cartInfo).then(response => {
        app.loading = false;
        if (response.data.cartId) {
          app.getCartList();
          app.openGoodsDialog = false;
          app.goodsSpecIds = [];
          app.goodsNum = 1; // 重置为1而不是0

          // 显示添加成功提示
          app.$message({
            message: `${app.goodsInfo.name} 已添加到购物车`,
            type: 'success',
            duration: 2000
          });
        }
      }).catch((err) => {
        app.loading = false;
        console.error('添加到购物车失败:', err);
        app.$message.error('添加到购物车失败，请重试');
      });
    },
    // 删除购物车
    removeFromCart(cartId) {
      const app = this;
      removeFromCart({ cartId: [cartId], userId: getUserId() }).then(response => {
        if (response.data) {
          app.getCartList();
        }
      }).catch(() => {
        // empty
      });
    },
    // 购物车数量变化
    changeBuyNum(cartInfo) {
      const app = this;
      // 确保 buyNum 是数字类型且至少为 1
      const buyNum = parseInt(cartInfo.buyNum) || 1;
      cartInfo.buyNum = buyNum;
      
      const param = { 
        goodsId: cartInfo.goodsId,
        skuId: cartInfo.skuId,
        cartId: cartInfo.cartId,
        action: '=',
        userId: app.memberInfo ? app.memberInfo.id : null,
        hangNo: app.hangNo,
        buyNum: buyNum 
      };
      app.loading = true;
      saveCart(param).then(response => {
        app.loading = false;
        if (response.data.cartId) {
          app.getCartList();
          app.openGoodsDialog = false;
          app.goodsSpecIds = [];
        }
      }).catch(() => {
        app.loading = false;
        app.getCartList();
      });
    },
    // 弹出关联会员
    switchMember() {
      this.openSwitchMemberDialog = true;
    },
    // 商品模式切换
    switchGoodMode(mode) {
      this.$cache.local.set("goodsMode", mode);
      this.goodsMode = mode;
      // 刷新当前分类的商品
      this.switchTab({name: this.navTab});
    },
    // 弹出关联员工
    bindStaff() {
      this.openBindStaffDialog = true;
    },
    // 获取会员等级名称
    getMemberGradeName(gradeId) {
      const app = this;
      if (gradeId) {
        getUserGradeInfo(gradeId).then(response => {
          if (response.data && response.data.userGradeInfo) {
            app.memberGradeName = response.data.userGradeInfo.name;
          } else {
            app.memberGradeName = '会员';
          }
        }).catch(() => {
          app.memberGradeName = '会员';
        });
      } else {
        app.memberGradeName = '会员';
      }
    },
    // 确认关联会员
    doSwitchMember(memberInfo) {
      this.openSwitchMemberDialog = false;
      if (memberInfo != 0) {
        this.memberInfo = memberInfo;
        if (memberInfo) {
          // 获取会员等级名称
          this.getMemberGradeName(memberInfo.gradeId);
          let cartIds = [];
          if (this.cartList && this.cartList.length > 0) {
            this.cartList.forEach(function(cart){
              cartIds.push(cart.cartId);
            })
          }
          setUserId(memberInfo.id);
          this.getCartList(cartIds);
        } else {
          this.memberGradeName = '';
          removeUserId();
          this.getCartList();
        }
      }
    },
    // 确认绑定员工
    doBindStaff(staff) {
      this.openBindStaffDialog = false;
      this.staffInfo = staff;
    },
    // 退出登录
    logout() {
      this.$router.push( '/' );
    },
    // 发起结算
    doSettlement() {
      if (this.cartList.length < 1) {
        this.$alert("请先添加结算商品！");
        return false;
      }
      this.getCartList();
      this.orderInfo = {};
      this.openSettlementDialog = true;
    },
    // 无商品结算
    doCashier() {
      this.orderInfo = {};
      this.openNoGoodsCashierDialog = true;
    },
    // 提交结算
    submitCashier(param) {
      this.orderInfo = {};
      this.openSettlementDialog = true;
      this.cartTotalPrice = parseFloat(param.amount);
      this.cartRemark = param.remark;
      this.openNoGoodsCashierDialog = false;
    },
    // 确认结算
    submitSettlement(param) {
      const app = this;
      // 已生成的订单支付
      if (app.orderInfo.id) {
        // 微信、支付宝支付
        if (param.payType == 'MICROPAY' || param.payType == 'ALISCAN') {
          app.payAmount = app.orderInfo.payAmount;
          app.orderId = app.orderInfo.id;
          app.openScanPayCodeDialog = true;
          app.openSettlementDialog = false;
          app.payType = param.payType;
        }
        // 现金、余额支付
        if (param.payType == 'CASH' || param.payType == 'BALANCE') {
          doPay({
            orderId: app.orderId,
            payType: param.payType,
            cashierPayAmount: param.totalPrice,
            cashierDiscountAmount: param.discountPrice,
            usePointExchange: param.usePointExchange || false,
            pointAmount: param.pointAmount || 0,
            pointDiscountAmount: param.pointDiscountAmount || 0,
            couponDiscountAmount: param.couponDiscountAmount || 0,
            discountAmount: param.discountAmount || '',
            reduceAmount: param.reduceAmount || '',
            userId: getUserId()
          }).then( response => {
            app.openSettlementDialog = false;
            if (response.data.orderInfo.payStatus == 'B') {
              app.showPayResult({ isSuccess: true, payAmount: response.data.orderInfo.payAmount, orderId: response.data.orderInfo.id });
            } else {
              app.$alert(response.data.message ? response.data.message : "抱歉，订单操作异常！");
            }
          })
        }
        return false;
      }
      // 购物车提交结算
      let cartIds = [];
      app.cartList.forEach(function(cart){
        cartIds.push(cart.cartId);
      })
      const data = { cashierPayAmount: param.totalPrice,
        cashierDiscountAmount: param.discountPrice,
        cartIds: cartIds.join(','),
        orderMode: 'oneself',
        payType: param.payType,
        remark: param.remark,
        type: app.cartList.length > 0 ? 'goods' : 'payment',
        couponId: param.userCouponId ? param.userCouponId : 0,
        usePointExchange: param.usePointExchange || false,
        pointAmount: param.pointAmount || 0,
        pointDiscountAmount: param.pointDiscountAmount || 0,
        couponDiscountAmount: param.couponDiscountAmount || 0,
        discountAmount: param.discountAmount || '',
        reduceAmount: param.reduceAmount || '',
        userId: getUserId(),
        staffId: app.staffInfo ? app.staffInfo.id : 0 };
      if (app.loading) {
        return false;
      }
      app.loading = true;
      submitSettlement(data).then( response => {
        app.loading = false;
        app.doSwitchMember(null);
        app.initCashier();
        app.getCartList();
        // 微信支付，弹出扫码框
        if (response.data.orderInfo.payType == 'MICROPAY' || param.payType == 'ALISCAN') {
          app.payAmount = response.data.orderInfo.payAmount;
          app.orderId = response.data.orderInfo.id;
          app.openScanPayCodeDialog = true;
          app.openSettlementDialog = false;
          app.payType = param.payType;
          return false;
        }
        // 现金、余额支付
        if (response.data.orderInfo.payType == 'CASH' || response.data.orderInfo.payType == 'BALANCE') {
          app.openSettlementDialog = false;
          if (response.data.orderInfo.payStatus == 'B') {
            app.showPayResult({ isSuccess: true, payAmount: response.data.orderInfo.payAmount, orderId: response.data.orderInfo.id });
          } else {
            app.$alert(response.data.message);
          }
          return false;
        }
        if (response.code == '201') {
          app.$alert(response.data.message);
        }
        return false;
      }).catch((err) => {
        app.loading = false;
        console.log(err.toString());
      });
    },
    // 点击挂单/取单
    hangUp() {
      this.openHangUpDialog = true;
    },
    // 取单
    getHangNo(data) {
      this.openHangUpDialog = false;
      this.hangNo = data.hangNo;
      this.getCartList();
      // 关联会员信息
      if (data.hangNo && data.hangNo.length > 0 ) {
        this.doSwitchMember(data.memberInfo);
      }
    },
    // 执行挂单
    doHangUp() {
      this.hangNo = '';
      this.getCartList();
      removeUserId();
      this.memberInfo = null;
    },
    // 关闭对话框
    closeDialog(dialog) {
      if (dialog == 'settlementDialog') {
        this.openSettlementDialog = false;
      } else if (dialog == 'switchMemberDialog') {
        this.openSwitchMemberDialog = false;
      } else if (dialog == 'scanPayCodeDialog') {
        this.openScanPayCodeDialog = false;
      } else if (dialog == 'payResultDialog') {
        this.openPayResultDialog = false;
      } else if (dialog == 'printOrder') {
        this.openOrderPrintDialog = false;
      } else if (dialog == 'hangUpDialog') {
        this.openHangUpDialog = false;
      } else if (dialog == 'openNoGoodsCashierDialog') {
        this.openNoGoodsCashierDialog = false;
      } else if (dialog == 'openBindStaffDialog') {
        this.openBindStaffDialog = false;
      }
    },
    // 展示支付结果
    showPayResult(payResult) {
      this.payResult = payResult;
      this.openPayResultDialog = true;
    },
    // 订单支付
    doPayOrder(orderInfo) {
      const app = this;
      app.payAmount = orderInfo.amount;
      app.orderId = orderInfo.id;
      app.orderInfo = orderInfo;
      let userId = 0
      if (orderInfo.isVisitor !== "Y") {
        userId = app.orderInfo.userInfo.id;
      }
      getMemberInfoById(userId).then(response => {
        if (response.data.memberInfo) {
          app.memberInfo = response.data.memberInfo;
          // 获取会员等级名称
          if (app.memberInfo.gradeId) {
            app.getMemberGradeName(app.memberInfo.gradeId);
          }
        } else {
          app.memberInfo = null;
          app.memberGradeName = '';
        }
      }).catch(() => {
        // empty
      });
      app.doSwitchMember(app.memberInfo);
      app.openSettlementDialog = true;
    },
    // 核销卡券
    doConfirmCoupon(code) {
      this.couponCode = code;
      this.confirmCoupon = true;
    },
    // 返回卡券列表
    doUserCoupon() {
      this.couponCode = false;
      this.confirmCoupon = false;
    },
    // 打印小票
    showOrderPrint(orderId) {
      const app = this;
      getOrderInfo(orderId).then(response => {
        if (response.data.orderInfo) {
          app.orderInfo = response.data.orderInfo;
          app.openOrderPrintDialog = true;
        }
      }).catch((err) => {
        app.loading = false;
        console.log(err.toString());
      });
    },
    // 确认关闭
    handleClose() {
      return false;
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  height: 100%;
  width: 100%;
  overflow: hidden;
}
.logout {
  display: none;
}

/* 添加黑色滤镜样式 */
.nav-item:nth-child(3) .cate-logo,
.nav-item:nth-child(4) .cate-logo {
  filter: brightness(0);
}

.main {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: row;
  background-color: #f5f5f5;
  overflow-x: hidden; /* 防止水平滚动条出现 */
  overflow-y: hidden; /* 防止垂直滚动条出现 */

  .left-side {
    width: 80px;
    height: 100%;
    background: #f5f5f5;  //导航栏背景色
    border: none;
    position: fixed;
    left: 0;
    top: 0;
    padding: 0;
    color: #FFFFFF;
    overflow-x: hidden;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    text-align: center;
    z-index: 999;

    @media (max-width: 992px) {
      width: 100px;
    }

    @media (max-width: 768px) {
      width: 80px;
    }

    // 添加顶部斜线装饰
    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 50px;
      background: linear-gradient(135deg, #40a9ff 10%, #1890ff 40%, #eee 80%);
      z-index: -1;
      border-bottom-right-radius: 5px;
      margin-top: -5px;
      margin-right: -5px;
    }

    .logo {
      height: 90px;
      padding: 20px 12px 10px 12px;
      font-weight: bold;

      .title {
        font-size: 14px;
        margin-bottom: 5px;
        color: black;

        @media (max-width: 768px) {
          font-size: 16px;
        }
      }

      .store {
        font-size: 12px;
        color: black;
      }

      .account {
        font-size: 12px;
        border: none;
        cursor: pointer;
        margin-top: 6px;
        padding: 4px;
        border-radius: 4px;
        color: black;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .cate {
      text-align: center;
      margin-left: 14px;
      margin-top: 20px;
      display: flex;
      flex-direction: column;
      height: 100%;

      @media (max-width: 992px) {
        margin-left: 10px;
      }

      @media (max-width: 768px) {
        margin-left: 5px;
      }

      .nav {
        list-style: none;
        display: flex;
        flex-direction: column;
        margin: 0px;
        padding: 5px;
        text-align: center;
        height: 100%;

        .nav-item {
          margin-top: 20px;
          font-size: 14px;
          width: 120px;
          text-align: center;
          margin-left: -32px;

          @media (max-width: 992px) {
            width: 100px;
          }

          @media (max-width: 768px) {
            width: 90px;
            font-size: 12px;
          }

          .nav-link {
            position: relative;
            padding: .5356875rem .9375rem;
            white-space: nowrap;
            text-align: center;
            font-weight: 600;
            color: #666666;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            border-radius: 5px;
            transition: color .2s linear;
            border: none;

            @media (max-width: 768px) {
              padding: .4rem .8rem;
            }

            .cate-logo {
              display: block;
              width: 40px;
              height: 40px;
              margin-bottom: 5px;

              @media (max-width: 768px) {
                width: 30px;
                height: 30px;
              }
            }
          }

          .active {
            font-weight: bold;
            color: #1890ff;
          }
        }
      }
    }
  }

  .content-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%; /* 使用视口高度而不是百分比 */
    width: calc(100% - 80px);
    margin-left: 80px;
    padding: 10px;
    box-sizing: border-box;
    overflow: hidden; /* 防止出现滚动条 */

    @media (max-width: 992px) {
      width: calc(100% - 100px);
      margin-left: 100px;
      padding: 8px;
    }

    @media (max-width: 768px) {
      width: calc(100% - 80px);
      margin-left: 80px;
      padding: 5px;
      flex-direction: column;
    }
  }

  .main-content {
    display: flex;
    width: 100%;
    height: calc(100% - 30px); /* 固定高度，防止整体页面出现滚动条 */
    overflow: hidden; /* 防止整体出现滚动条 */

    @media (max-width: 768px) {
      flex-direction: column;
      height: calc(100vh - 100px);
    }
  }

  .goods-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    margin-right: 10px;
    background-color: white;
    border-radius: 5px;
    padding: 0;
    max-width: calc(100% - 320px); /* 限制最大宽度，防止影响布局 */

    @media (max-width: 1200px) {
      max-width: calc(100% - 330px);
    }

    @media (max-width: 992px) {
      max-width: calc(100% - 290px);
    }

    @media (max-width: 768px) {
      margin-right: 0;
      margin-bottom: 10px;
      height: 60vh;
      max-width: 100%;
    }

    .title {
      padding-top: 10px;
      position: relative;
      top: 0;
      height: auto;
      width: 100%;
      background: #ffffff;
      padding: 10px;
      z-index: 10;
      border-radius: 5px;
      margin-bottom: 5px;

      @media (max-width: 992px) {
        padding: 8px;
      }

      @media (max-width: 768px) {
        padding: 5px;
      }

      .search-form {
        height: 50px;
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        @media (max-width: 768px) {
          height: auto;
        }

        .form-item {
          flex: 1;
          margin-right: 10px;
          margin-top: -40px;
          min-width: 200px;

          @media (max-width: 992px) {
            min-width: 150px;
          }
        }

        .input-item {
          width: 240%;
          margin-top: 0px;

          @media (max-width: 1200px) {
            width: 180%;
          }

          @media (max-width: 992px) {
            width: 150%;
          }

          @media (max-width: 768px) {
            width: 120%;
          }
        }

        .search-goods {
          height: 40px;
          background: #1890ff;
          color: #fff;
          margin-top: -40px;
          margin-right: 5px;

          @media (max-width: 768px) {
            margin-right: 10px;
            padding: 9px 15px;
          }
        }

        .setting {
          display: flex;
          margin-right: 0;
          flex-wrap: wrap;

          .item {
            margin-left: 15px;
            color: #333;
            cursor: pointer;
            display: flex;
            align-items: center;

            @media (max-width: 768px) {
              margin-left: 10px;
              margin-top: 5px;
            }
          }

          .active {
            font-weight: bold;
            color: #1890ff;
          }

          .icon {
            width: 14px;
            height: 14px;
            vertical-align: middle;
            margin-right: 4px;
          }

          .text {
            font-size: 14px;

            @media (max-width: 768px) {
              font-size: 12px;
            }
          }
        }
      }

      .tab-box {
        margin-top: 30px;
        width: 100%;
        overflow-x: auto;
        border-bottom: 1px solid #ddd;

        &::-webkit-scrollbar {
          height: 4px;
        }

        &::-webkit-scrollbar-thumb {
          background: #ddd;
          border-radius: 2px;
        }
      }
    }

    .goods-list-container {
      flex: 1;
      background-color: white;
      border-radius: 5px;
      padding: 5px;
      overflow-y: auto;
      position: relative;
      display: flex;
      flex-direction: column;
      height: calc(100vh - 225px); /* 固定高度，防止影响整体布局 */
      min-height: 300px; /* 最小高度 */
      /* 自定义滚动条样式 */
      &::-webkit-scrollbar {
        width: 6px;
      }
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }
      &::-webkit-scrollbar-thumb {
        background: #ccc;
        border-radius: 3px;
      }
      &::-webkit-scrollbar-thumb:hover {
        background: #aaa;

      }

      @media (max-width: 992px) {
        padding: 10px;
      }

      @media (max-width: 768px) {
        padding: 8px;
        height: calc(100vh - 300px);
      }
    }

    .goods-list-small {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
      gap: 15px;
      flex: 1 1 auto;
      overflow-y: visible;
      height: 100%;
      padding-right: 5px; /* 为内部滚动条预留空间 */

      @media (max-width: 1200px) {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 12px;
      }

      @media (max-width: 992px) {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 10px;
      }

      @media (max-width: 768px) {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 8px;
      }

      .goods-item {
        .item {
          background: #ffffff;
          padding: 10px;
          border-radius: 5px;
          border: solid 1px #eee;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          cursor: pointer;
          transition: all 0.2s;

          @media (max-width: 768px) {
            padding: 8px;
          }

          &:hover:not(.out-of-stock) {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
          }

          &.out-of-stock {
            opacity: 0.6;
            background: #f5f5f5;
            border-color: #d9d9d9;

            &:hover {
              transform: none;
              box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }
          }

          .image-container {
            position: relative;
            width: 100%;

            .image {
              width: 100%;
              height: 120px;
              border-radius: 5px;
              object-fit: cover;

              @media (max-width: 1200px) {
                height: 110px;
              }

              @media (max-width: 992px) {
                height: 100px;
              }

              @media (max-width: 768px) {
                height: 90px;
              }
            }

            .sold-out-overlay {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: rgba(0, 0, 0, 0.6);
              border-radius: 5px;
              display: flex;
              align-items: center;
              justify-content: center;

              .sold-out-text {
                color: white;
                font-size: 14px;
                font-weight: bold;
                text-align: center;
                padding: 4px 8px;
                background: rgba(255, 77, 79, 0.9);
                border-radius: 4px;

                @media (max-width: 768px) {
                  font-size: 12px;
                  padding: 3px 6px;
                }
              }
            }
          }

          .out-of-stock & .image {
            filter: grayscale(100%);
          }

          .goods-name {
            margin-top: 8px;
            font-size: 14px;
            color: #333;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;

            @media (max-width: 768px) {
              font-size: 12px;
              margin-top: 6px;
            }
          }

          .goods-price {
            color: #ff4d4f;
            font-size: 14px;
            font-weight: bold;
            margin-top: 5px;

            @media (max-width: 768px) {
              font-size: 12px;
              margin-top: 4px;
            }
          }
        }
      }
    }

    .goods-list-big {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      gap: 20px;
      flex: 1 1 auto;
      overflow-y: visible;
      height: 100%;
      padding-right: 5px; /* 为内部滚动条预留空间 */

      @media (max-width: 1200px) {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: 15px;
      }

      @media (max-width: 992px) {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 12px;
      }

      @media (max-width: 768px) {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 10px;
      }

      .goods-item {
        .item {
          background: #ffffff;
          padding: 10px;
          border-radius: 5px;
          border: solid 1px #eee;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          cursor: pointer;
          transition: all 0.2s;

          @media (max-width: 768px) {
            padding: 8px;
          }

          &:hover:not(.out-of-stock) {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
          }

          &.out-of-stock {
            opacity: 0.6;
            background: #f5f5f5;
            border-color: #d9d9d9;

            &:hover {
              transform: none;
              box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }
          }

          .image-container {
            position: relative;
            width: 100%;

            .image {
              width: 100%;
              height: 180px;
              border-radius: 5px;
              object-fit: cover;

              @media (max-width: 1200px) {
                height: 160px;
              }

              @media (max-width: 992px) {
                height: 140px;
              }

              @media (max-width: 768px) {
                height: 120px;
              }
            }

            .sold-out-overlay {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: rgba(0, 0, 0, 0.6);
              border-radius: 5px;
              display: flex;
              align-items: center;
              justify-content: center;

              .sold-out-text {
                color: white;
                font-size: 16px;
                font-weight: bold;
                text-align: center;
                padding: 6px 12px;
                background: rgba(255, 77, 79, 0.9);
                border-radius: 4px;

                @media (max-width: 992px) {
                  font-size: 14px;
                  padding: 5px 10px;
                }

                @media (max-width: 768px) {
                  font-size: 13px;
                  padding: 4px 8px;
                }
              }
            }
          }

          .out-of-stock & .image {
            filter: grayscale(100%);
          }

          .goods-name {
            margin-top: 10px;
            font-size: 16px;
            color: #333;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;

            @media (max-width: 992px) {
              font-size: 14px;
              margin-top: 8px;
            }

            @media (max-width: 768px) {
              font-size: 13px;
              margin-top: 6px;
            }
          }

          .goods-price {
            color: #ff4d4f;
            font-size: 16px;
            font-weight: bold;
            margin-top: 5px;

            @media (max-width: 992px) {
              font-size: 14px;
            }

            @media (max-width: 768px) {
              font-size: 13px;
              margin-top: 4px;
            }
          }
        }
      }
    }
  }

  .cart-container {
    width: 25%;
    min-width: 15%;
    height: 100%;
    background: #FFFFFF;
    display: flex;
    flex-direction: column;
    margin-right: 0px;
    border-radius: 8px;
    overflow: hidden;

    @media (max-width: 1200px) {
      width: 320px;
      min-width: 320px;
    }

    @media (max-width: 992px) {
      width: 280px;
      min-width: 280px;
    }

    @media (max-width: 768px) {
      width: 100%;
      min-width: 100%;
      height: 40vh;
      margin-right: 0;
    }

    .cart-user-info {
      background-color: #1890ff;
      color: white;
      padding: 15px;
      border-radius: 8px 8px 8px 8px;

      @media (max-width: 992px) {
        padding: 12px;
      }

      .user-info {
        position: relative;

        .user-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .user-name {
            font-size: 18px;
            font-weight: bold;

            @media (max-width: 992px) {
              font-size: 16px;
            }
          }

          .user-amount {
            font-size: 20px;
            font-weight: bold;

            @media (max-width: 992px) {
              font-size: 18px;
            }
          }
        }

        .user-details {
          .detail-row {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;

            @media (max-width: 992px) {
              margin-top: 20px;
              flex-direction: column;
            }

            .phone-number {
              font-size: 14px;

              @media (max-width: 992px) {
                font-size: 12px;
                margin-bottom: 5px;
              }
            }

            .balance {
              margin-left: 70px;

              @media (max-width: 992px) {
                margin-left: 0;
                font-size: 12px;
              }
            }
          }

          .tag-row {
            margin-top: 10px;
            font-size: 12px;

            .member-tag {
              display: inline-block;
              background-color: #ffcc00;
              color: #333;
              font-size: 12px;
              padding: 2px 8px;
              border-radius: 12px;
              font-weight: bold;
            }
          }

          .member-card {
            margin-top: 10px;
            font-size: 12px;

            .member-card-number {
              font-size: 14px;

              @media (max-width: 992px) {
                font-size: 12px;
              }
            }
          }
        }

        .switch-btn {
          position: absolute;
          right: 0;
          top: 0;
          background-color: white;
          color: #1890ff;
          border: none;
          font-size: 12px;
          padding: 5px 10px;
          border-radius: 4px;

          @media (max-width: 992px) {
            padding: 4px 8px;
            font-size: 11px;
          }
        }
      }
    }

    .carts {
      flex: 1;
      overflow-y: auto;
      padding: 10px;

      @media (max-width: 992px) {
        padding: 8px;
      }

      .tab {
        .cart-list {
          .cart-item {
            display: flex;
            padding: 10px;
            border-bottom: 1px solid #eee;
            position: relative;
            margin-bottom: 10px;

            @media (max-width: 992px) {
              padding: 8px;
              margin-bottom: 8px;
            }

            .image {
              width: 60px;
              height: 60px;
              border-radius: 4px;
              object-fit: cover;
              margin-right: 10px;
              border: 1px solid #eee;

              @media (max-width: 992px) {
                width: 50px;
                height: 50px;
              }
            }

            .info {
              flex: 1;

              .name {
                font-size: 14px;
                font-weight: bold;
                margin-bottom: 5px;
                color: #333;

                @media (max-width: 992px) {
                  font-size: 13px;
                  margin-bottom: 4px;
                }
              }

              .spec {
                display: flex;
                flex-wrap: wrap;
                margin-bottom: 5px;

                .item {
                  background-color: #f0f9ff;
                  padding: 2px 6px;
                  border-radius: 3px;
                  font-size: 12px;
                  margin-right: 5px;
                  margin-bottom: 5px;
                  color: #1890ff;

                  @media (max-width: 992px) {
                    font-size: 11px;
                    padding: 1px 5px;
                  }
                }
              }

              .num {
                .input {
                  width: 100px;

                  @media (max-width: 992px) {
                    width: 80px;
                  }
                }
              }
            }

            .option {
              display: flex;
              flex-direction: column;
              align-items: flex-end;
              justify-content: space-between;

              .remove {
                font-size: 16px;
                color: #999;
                cursor: pointer;
                &:hover {
                  color: #ff4d4f;
                }

                @media (max-width: 992px) {
                  font-size: 14px;
                }
              }

              .total {
                color: #ff4d4f;
                font-weight: bold;
                font-size: 14px;

                @media (max-width: 992px) {
                  font-size: 13px;
                }
              }
            }
          }
        }

        .empty {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 200px;

          @media (max-width: 768px) {
            height: 150px;
          }
        }
      }
    }

    .cart-footer {
      padding: 15px;
      border-top: 1px solid #eee;

      @media (max-width: 992px) {
        padding: 12px;
      }

      .cart-total {
        margin-bottom: 15px;

        @media (max-width: 992px) {
          margin-bottom: 12px;
        }

        .total-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 5px;
          font-size: 14px;

          @media (max-width: 992px) {
            font-size: 13px;
          }

          .num {
            color: #1890ff;
            font-weight: bold;
          }

          .price {
            color: #ff4d4f;
            font-weight: bold;
            font-size: 16px;

            @media (max-width: 992px) {
              font-size: 14px;
            }
          }
        }
      }

      .cart-buttons {
        display: flex;
        gap: 10px;

        @media (max-width: 992px) {
          gap: 8px;
        }

        .cart-button {
          flex: 1;
          padding: 10px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
          font-weight: bold;

          @media (max-width: 992px) {
            padding: 8px;
            font-size: 13px;
          }

          &.secondary {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            color: #333;
            &:hover {
              background-color: #eee;
            }
          }

          &.primary {
            background-color: #1890ff;
            color: white;
            &:hover {
              background-color: #40a9ff;
            }
          }
        }
      }
    }
  }
}
.top-navbar {
  position: relative;
  border-radius: 8px 8px 8px 8px;
  width: 100%;
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(199, 73, 73, 0.1);
  z-index: 10;
  margin-bottom: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  margin-top: -8px;
}

.navbar-content {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-title {
  color: black;
  font-weight: bold;
}

.welcome-text {
  font-size: 16px;

  @media (max-width: 768px) {
    font-size: 14px;
  }
}

.goods-info {
  border: solid 1px #eee;
  padding: 30px;
  border-radius: 5px;

  @media (max-width: 992px) {
    padding: 20px;
  }

  @media (max-width: 768px) {
    padding: 15px;
  }

  .name {
    height: 40px;
    font-weight: bold;
    font-size: 20px;

    @media (max-width: 992px) {
      font-size: 18px;
    }

    @media (max-width: 768px) {
      font-size: 16px;
      height: auto;
    }
  }

  .price {
    height: 40px;
    color: #ff4d4f;
    font-size: 16px;

    @media (max-width: 768px) {
      font-size: 14px;
      height: auto;
    }
  }
  
  .num {
    margin: 15px 0;
    
    .input {
      width: 150px;
    }
  }

  .spec-list {
    border: solid 1px #eee;
    padding: 20px;
    margin-top: 10px;
    border-radius: 6px;

    @media (max-width: 992px) {
      padding: 15px;
    }

    @media (max-width: 768px) {
      padding: 10px;
    }

    .spec-item {
      margin-bottom: 20px;

      .spec-name {
        font-weight: bold;
        font-size: 16px;

        @media (max-width: 768px) {
          font-size: 14px;
        }
      }

      .values {
        display: flex;
        flex-wrap: wrap;
        padding-top: 10px;
        gap: 10px;
        font-size: 14px;

        @media (max-width: 768px) {
          gap: 8px;
          font-size: 12px;
        }

        .value {
          border: solid 1px #eee;
          padding: 6px 12px;
          cursor: pointer;
          border-radius: 4px;
          background: #f5f5f5;
          color: #333;

          @media (max-width: 768px) {
            padding: 4px 10px;
          }

          &:hover {
            border-color: #1890ff;
          }
        }

        .active {
          border: solid 1px #1890ff;
          background: #1890ff;
          color: white;
        }
      }
    }
  }
}

.logout-item {
  margin-top: auto !important;
  padding-bottom: 20px;
  margin-bottom: 0;
}

.logout-icon {
  font-size: 24px;
  color: red;
  display: flex !important;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.logout-link:hover .logout-icon {
  color: #1890ff;
}

.logout-link span {
  color: red;
  font-weight: bold;
}
</style>
<style scoped>
.input-item >>> .el-input__inner {
  border: #1890ff solid 1px;
  height: 40px;
}
.form-item >>> .el-form-item__label {
  line-height: 40px;
  height: 40px;
}
.el-tabs--border-card {
  box-shadow: none;
  border: none;
}
::v-deep .el-tabs--card > .el-tabs__header {
  border-bottom: none;
}
::v-deep .el-tabs--card > .el-tabs__header .el-tabs__nav {
  border: none;
}
::v-deep .el-tabs--card > .el-tabs__header .el-tabs__item {
  border: none;
  margin-right: 5px;
}
::v-deep .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  color: #1890ff;
  border-bottom: 2px solid #1890ff;
  background-color: transparent;
}
::v-deep .el-tabs--border-card > .el-tabs__content {
  padding: 0;
}
::v-deep .el-pagination.is-background .btn-next,
::v-deep .el-pagination.is-background .btn-prev,
::v-deep .el-pagination.is-background .el-pager li {
  background-color: #f5f5f5;
  color: #606266;
}
::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: #00afff;
  color: #fff;
}
::v-deep .el-pagination button {
  color: #606266;
}
::v-deep .el-pagination .btn-prev,
::v-deep .el-pagination .btn-next {
  background-color: #f4f4f5;
}
::v-deep .el-select .el-input .el-input__inner {
  color: #606266;
}
::v-deep .el-pagination__jump {
  color: #606266;
}
::v-deep .el-pagination__editor.el-input .el-input__inner {
  color: #606266;
}
::v-deep .el-input-number .el-input-number__decrease,
::v-deep .el-input-number .el-input-number__increase {
  background-color: white;
  border: 1px solid #ddd;
  color: #333;
}

/* 添加以下样式确保购物车中的数量输入框正确显示 */
::v-deep .el-input-number .el-input__inner {
  color: #333;
  text-align: center;
  padding: 0 30px;  /* 为左右两侧的按钮留出空间 */
  background-color: white;
}

/* 删除右侧控制的样式 */
/* ::v-deep .el-input-number.is-controls-right .el-input__inner {
  padding-right: 25px;
} */

::v-deep .carts .el-input-number {
  width: 120px;  /* 加宽一些，适应左右控制按钮 */
  margin: 0;
}

::v-deep .carts .el-input-number .el-input__inner {
  height: 32px;
  line-height: 32px;
  border: 1px solid #ddd;
}

/* 改进左右按钮的样式 */
::v-deep .el-input-number .el-input-number__decrease,
::v-deep .el-input-number .el-input-number__increase {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  color: #333;
  height: 32px;
  width: 32px;
  line-height: 30px;
  text-align: center;
}

::v-deep .el-input-number .el-input-number__decrease:hover,
::v-deep .el-input-number .el-input-number__increase:hover {
  background-color: #e6e6e6;
}
</style>
