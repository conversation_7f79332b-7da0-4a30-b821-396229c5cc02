<template>
  <div class="member-container">
    <el-form :model="queryParams" class="search-form" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="会员ID" prop="id">
        <el-input
            v-model="queryParams.id"
            placeholder="请输入会员ID"
            clearable
            class="input-item"
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="会员号" prop="userNo">
        <el-input
            v-model="queryParams.userNo"
            placeholder="请输入会员号"
            clearable
            class="input-item"
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input
            v-model="queryParams.mobile"
            placeholder="请输入会员手机号"
            clearable
            class="input-item"
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input
            v-model="queryParams.name"
            placeholder="请输入会员名称"
            clearable
            class="input-item"
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="会员等级" prop="gradeId">
        <el-select
            v-model="queryParams.gradeId"
            clearable
            placeholder="会员等级"
            class="input-item"
        >
          <el-option v-for="grade in userGradeList" :key="grade.id+''" :label="grade.name" :value="grade.id+''"/>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
            v-model="queryParams.status"
            placeholder="状态"
            clearable
            class="input-item"
        >
          <el-option key="A" label="启用" value="A"/>
          <el-option key="N" label="禁用" value="N"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" class="main-button-middle" icon="el-icon-search" @click="handleQuery" style="background: #00afff;border: 1px solid #00afff;">查询</el-button>
        <el-button icon="el-icon-refresh" class="main-button-middle main-button-reset" @click="resetQuery">重置</el-button>
        <el-button
            plain
            icon="el-icon-plus"
            class="main-button-middle main-button-reset"
            @click="handleAdd"
            v-hasPermi="['member:add']"
        >新增</el-button>
      </el-form-item>
    </el-form>

    <div class="table-container">
      <el-table ref="tables" v-loading="loading" :data="list" border style="width: 100%;" fit height="100%" @selection-change="handleSelectionChange" :default-sort="defaultSort" @sort-change="handleSortChange">
        <el-table-column label="会员ID" prop="id" width="80"/>
        <el-table-column label="头像" align="center" width="80">
          <template slot-scope="scope">
            <img v-if="scope.row.avatar" class="list-avatar" :src="scope.row.avatar">
            <img v-else class="list-avatar" src="@/assets/images/avatar.png">
          </template>
        </el-table-column>
        <el-table-column label="会员号" prop="userNo" width="150"/>
        <el-table-column label="名称" align="center" prop="name" />
        <el-table-column label="手机号" align="center" prop="mobile">
          <template slot-scope="scope">
            <span>{{ scope.row.mobile ? scope.row.mobile : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="会员等级" align="center" prop="gradeId">
          <template slot-scope="scope">
            <span>{{ scope.row.gradeId ? getName(userGradeList, scope.row.gradeId) : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="余额" align="center" prop="balance">
          <template slot-scope="scope">
            <div><span style="color:red;">￥{{ scope.row.balance ? scope.row.balance.toFixed(2) : '0.00' }}</span></div>
            <el-button
                class="main-button-mini"
                type="primary"
                size="mini"
                @click="handleBalance(scope.row.id)"
                v-hasPermi="['balance:modify']"
            >充值</el-button>
          </template>
        </el-table-column>
        <el-table-column label="积分" align="center" prop="point">
          <template slot-scope="scope">
            <div><span>{{ scope.row.point ? scope.row.point : '0.00' }}</span></div>
            <el-button
                class="main-button-mini"
                type="primary"
                size="mini"
                @click="handlePoint(scope.row.id)"
                v-hasPermi="['point:modify']"
            >变更</el-button>
          </template>
        </el-table-column>
        <el-table-column label="注册时间" align="center" width="160" prop="createTime">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="活跃时间" align="center" width="160" prop="updateTime">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.updateTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" width="80" prop="status">
          <template slot-scope="scope">
            <el-switch
                v-model="scope.row.status"
                active-value="A"
                inactive-value="N"
                active-color="#00acac"
                @change="handleStatusChange(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200" fixed='right'>
          <template slot-scope="scope">
            <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                class="main-text"
                v-hasPermi="['cashier:index']"
                @click="handleUpdate(scope.row)"
            >修改</el-button>
            <el-button
                size="mini"
                type="text"
                icon="el-icon-warning"
                class="main-text"
                v-hasPermi="['cashier:index']"
                @click="handleReportLoss(scope.row)"
            >挂失</el-button>
            <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                class="main-text"
                v-hasPermi="['cashier:index']"
                @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <pagination
        v-show="total>0"
        :total="total"
        class="pagination"
        :page.sync="queryParams.page"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" :visible.sync="open" class="common-dialog" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="会员名称" prop="name" style="width: 420px">
              <el-input v-model="form.name" placeholder="请输入会员名称" maxlength="30" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="会员等级" prop="gradeId">
              <el-select
                  v-model="form.gradeId"
                  placeholder="会员等级"
                  style="width: 300px"
              >
                <el-option v-for="grade in userGradeList" :key="grade.id+''" :label="grade.name" :value="grade.id+''"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="邀请人" prop="shareId">
              <el-select
                  v-model="form.shareId"
                  filterable
                  remote
                  reserve-keyword
                  placeholder="请输入会员手机号查询"
                  :remote-method="remoteSearchMember"
                  :loading="vipMemberLoading"
                  style="width: 300px"
                  :disabled="!!form.id"
              >
                <el-option
                  v-for="item in vipMemberOptions"
                  :key="item.id"
                  :label="item.name + ' (' + (item.mobile || '') + ')'"
                  :value="item.id.toString()"
                  :disabled="getInviterSubCount(item.id) >= 50"
                >
                  <span>{{ item.name }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">
                    {{ item.mobile || '' }}
                    <span v-if="getInviterSubCount(item.id) !== null" style="margin-left: 5px;">
                      (下级: {{ getInviterSubCount(item.id) }}/50)
                      <span v-if="getInviterSubCount(item.id) >= 50" style="color: #f56c6c;">已满</span>
                    </span>
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="订单选择" prop="orderId">
              <el-select
                  v-model="form.orderId"
                  filterable
                  remote
                  reserve-keyword
                  placeholder="请输入订单号查询订单"
                  :remote-method="remoteSearchOrder"
                  :loading="orderLoading"
                  style="width: 300px"
                  :disabled="!!form.id"
                  clearable
              >
                <el-option
                  v-for="item in orderOptions"
                  :key="item.id"
                  :label="item.orderSn + ' (￥' + item.amount + ') - ' + (item.userInfo ? item.userInfo.name : '普通用户')"
                  :value="item.id.toString()"
                >
                  <span>{{ item.orderSn }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">￥{{ item.amount }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="有效期限">
              <el-date-picker
                  v-model="form.startTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="datetime"
                  placeholder="开始时间"
                  :picker-options="startTimeOptions"
              ></el-date-picker>
              <span class="sp"> 至 </span>
              <el-date-picker
                  v-model="form.endTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="datetime"
                  placeholder="结束时间"
                  :picker-options="endTimeOptions"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="会员号" prop="userNo">
              <el-input v-model="form.userNo" placeholder="请输入会员号，为空系统将自动分配" maxlength="30" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="手机号" prop="mobile">
              <el-input v-model="form.mobile" placeholder="请输入手机号" maxlength="30"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="性别" prop="sex">
              <el-radio-group v-model="form.sex">
                <el-radio :key="1" :label="1" :value="1">男</el-radio>
                <el-radio :key="0" :label="0" :value="0">女</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="身份证号" prop="idcard">
              <el-input v-model="form.idcard" placeholder="请输入身份证号" maxlength="30"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="生日" prop="birthday">
              <el-input v-model="form.birthday" placeholder="请输入生日，格式如：1990-01-01" maxlength="30"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="通讯地址" prop="address">
              <el-input v-model="form.address" placeholder="请输入通讯地址" maxlength="100"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="会员状态">
              <el-radio-group v-model="form.status">
                <el-radio key="A" label="A" value="A">正常</el-radio>
                <el-radio key="N" label="N" value="N">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="form.id">
          <el-col :span="24">
            <el-form-item label="注册来源">
              <el-radio-group v-model="form.source">
                <el-radio label="" v-if="!form.source">未知</el-radio>
                <el-radio label="wechat_login" v-if="form.source == 'wechat_login'">微信小程序</el-radio>
                <el-radio label="wechat_mp" v-if="form.source == 'wechat_mp'">微信公众号</el-radio>
                <el-radio label="backend_add" v-if="form.source == 'backend_add'">后台添加</el-radio>
                <el-radio label="register_by_account" v-if="form.source == 'register_by_account'">H5注册</el-radio>
                <el-radio label="mobile_login" v-if="form.source == 'mobile_login'">手机号登录注册</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注信息">
              <el-input v-model="form.description" type="textarea" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" class="main-button" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 余额充值对话框 -->
    <balanceRecharge :showDialog="openBalance" :userId="userId" @closeDialog="closeDialog" @close="closeDialog" append-to-body/>

    <!-- 积分充值对话框 -->
    <pointRecharge :showDialog="openPoint" :userId="userId" @closeDialog="closeDialog" @close="closeDialog" append-to-body/>
  </div>
</template>

<script>
import { getMemberList, updateMemberStatus, getMemberInfo, saveMember, deleteMember, reportLoss, getCommissionRelationList } from "@/api/member";
import { getOrderList } from "@/api/order";
import balanceRecharge from "./balanceRecharge";
import pointRecharge from "./pointRecharge";
import { Message } from "element-ui";
export default {
  name: "MemberIndex",
  components: { balanceRecharge, pointRecharge },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 标题
      title: "",
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 会员等级列表
      userGradeList: [],
      // 是否显示修改对话框
      open: false,
      // 当前操作用户
      userId: '',
      // 是否弹层充值
      openBalance: false,
      // 是否弹层积分
      openPoint: false,
      // 日期范围
      dateRange: [],
      // 分享人id
      shareId:'',
      // VIP会员选项列表
      vipMemberOptions: [],
      // VIP会员加载状态
      vipMemberLoading: false,
      // 邀请人下级数量映射
      inviterSubCountMap: {},
      // 订单选项列表
      orderOptions: [],
      // 订单加载状态
      orderLoading: false,
      // 默认排序
      defaultSort: {prop: 'updateTime', order: 'descending'},
      // 表单参数
      form: { id: '', name: '', gradeId: '', mobile: '', userNo: '', startTime: '', endTime: '', sex: 1, idcard: '', birthday: '', address: '', status: "A", description: '', shareId: '', orderId: '' },
      // 查询参数
      queryParams: {
        page: 1,
        pageSize: 10,
        mobile: "",
        name: "",
        gradeId: "",
        userId: "",
        userNo: "",
        status: ""
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: "会员名称不能为空", trigger: "blur" },
          { min: 2, max: 200, message: '会员名称长度必须介于2 和 100 之间', trigger: 'blur' }
        ],
        gradeId: [{ required: true, message: "请选择会员等级", trigger: "blur" }]
      },
      // 开始时间选项
      startTimeOptions: {
        disabledDate: (time) => {
          return time.getTime() < Date.now() - 86400000; // 禁用过去的时间
        }
      }
    };
  },
  computed: {
    // 结束时间选项
    endTimeOptions() {
      const self = this;
      return {
        disabledDate(time) {
          if (self.form && self.form.startTime) {
            const startTime = new Date(self.form.startTime).getTime();
            return time.getTime() < startTime; // 禁用早于开始时间的日期
          }
          return false;
        }
      };
    }
  },
  created() {
    this.getList();
  },
  methods: {
    // 查询列表
    getList() {
      this.loading = true;
      getMemberList(this.addDateRange(this.queryParams, this.dateRange)).then( response => {
            this.list = response.data.paginationResponse.content;
            this.total = response.data.paginationResponse.totalElements;
            this.userGradeList = response.data.userGradeList
            this.loading = false;
          }
      ).catch(() => {
        // empty
      });
    },
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    // 重置按钮操作
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order);
      this.handleQuery();
    },
    // 状态修改
    handleStatusChange(row) {
      let text = row.status == "A" ? "启用" : "禁用";
      this.$confirm('确认要' + text + '"' + row.name + '"吗？').then(function() {
        return updateMemberStatus(row.id, row.status);
      }).then(() => {
        Message({
          message: "提示：" + text + "成功",
          type: "success"
        });
      }).catch(function() {
        row.status = row.status === "N" ? "A" : "N";
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    // 排序触发事件
    handleSortChange(column, prop, order) {
      this.queryParams.orderByColumn = column.prop;
      this.queryParams.isAsc = column.order;
      this.getList();
    },
    // 余额充值操作
    handleBalance(userId) {
      this.openBalance = true;
      this.userId = userId.toString();
    },
    // 积分变更操作
    handlePoint(userId) {
      this.openPoint = true
      this.userId = userId.toString();
    },
    // 关闭对话框
    closeDialog(dialog) {
      if (dialog == 'balance') {
        this.openBalance = false;
      }
      if (dialog == 'point') {
        this.openPoint = false;
      }
      this.userId = "";
      this.getList();
    },
    // 新增按钮操作
    handleAdd() {
      this.reset();
      // 设置默认开始时间为当前时间
      this.form.startTime = this.formatDateTime(new Date());
      this.open = true;
      this.title = "新增会员";
    },
    // 格式化日期时间
    formatDateTime(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    // 表单重置
    reset() {
      this.resetForm("form");
      this.form.id = '';
      this.form.description = '';
      this.form.name = '';
      this.form.endTime = '';
      this.form.gradeId = '';
      this.form.userNo = '';
      this.form.mobile = '';
      this.form.shareId = '';
      this.form.orderId = '';
      // 清空邀请人选项列表
      this.vipMemberOptions = [];
      // 清空订单选项列表
      this.orderOptions = [];
      // 清空邀请人下级数量缓存
      this.inviterSubCountMap = {};
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
      // 清空邀请人选项
      this.vipMemberOptions = [];
      // 清空订单选项
      this.orderOptions = [];
      // 清空邀请人下级数量缓存
      this.inviterSubCountMap = {};
    },
    // 提交按钮
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 检查邀请人下级数量限制
          if (this.form.shareId && !this.form.id) { // 只在新增时检查
            const inviterSubCount = this.getInviterSubCount(parseInt(this.form.shareId));
            if (inviterSubCount !== null && inviterSubCount >= 50) {
              this.$message.error('该邀请人下级名额已满，请重新选择邀请人');
              return;
            }
          }

          // 创建一个新对象用于提交，避免修改原始form对象
          const submitData = { ...this.form };

          if (!submitData.shareId) {
            delete submitData.shareId;
          }

          if (!submitData.orderId) {
            delete submitData.orderId;
          }
          
          if (this.form.id) {
            // 编辑模式下，保留原有邀请人数据，不提交shareId和orderId字段
            delete submitData.shareId;
            delete submitData.orderId;
            
            saveMember(submitData).then(response => {
              Message({
                message: "提示：修改会员成功",
                type: "success"
              });
              this.open = false;
              this.getList();
            }).catch(() => {
              // empty
            });
          } else {
            saveMember(submitData).then(response => {
              Message({
                message: "提示：新增会员成功",
                type: "success"
              });
              this.open = false;
              this.getList();
            }).catch(() => {
              // empty
            });
          }
        }
      });
    },
    // 修改按钮操作
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getMemberInfo(id).then(response => {
        this.form = response.data.memberInfo;
        
        if (!this.form.startTime) {
          this.form.startTime = this.formatDateTime(new Date());
        }
        
        if (response.data.referrer) {
          const referrer = response.data.referrer;
          this.form.shareId = referrer.id.toString();
          
          this.vipMemberOptions = [{
            id: referrer.id,
            name: referrer.name,
            mobile: referrer.mobile
          }];
        }
        
        this.open = true;
        this.title = "编辑会员";
      }).catch(() => {
        // empty
      });
    },
    // 删除按钮操作
    handleDelete(row) {
      const name = row.name
      this.$confirm('确定删除"' + name + '"的会员信息？').then(function() {
        return deleteMember(row.id);
      }).then(() => {
        this.getList();
        Message({
          message: "提示：删除成功",
          type: "success"
        });
      }).catch(() => {});
    },
    // 挂失按钮操作
    handleReportLoss(row) {
      const name = row.name;
      const mobile = row.mobile;
      const userId = (row.id).toString();
      if (!mobile) {
        Message({
          message: "提示：该会员没有手机号，无法挂失",
          type: "warning"
        });
        return;
      }
      this.$confirm('确定要挂失会员"' + name + '"吗？').then(() => {
        return reportLoss(userId);
      }).then(() => {
        Message({
          message: "提示：挂失成功",
          type: "success"
        });
      }).catch(() => {
        // 用户取消或请求失败
      });
    },
    // 远程搜索会员
    remoteSearchMember(query) {
      if (query !== '') {
        this.vipMemberLoading = true;
        // 构建查询参数，只传递手机号参数
        const searchParams = {
          page: 1,
          pageSize: 10,
          mobile: query // 只通过手机号查询
        };

        // 使用现有的getMemberList接口查询会员
        getMemberList(searchParams).then(async response => {
          // 获取查询结果，不再过滤VIP会员，直接使用查询到的所有会员
          this.vipMemberOptions = response.data.paginationResponse.content || [];

          // 为每个会员查询下级数量
          for (const member of this.vipMemberOptions) {
            await this.queryInviterSubCount(member.id);
          }

          this.vipMemberLoading = false;
        }).catch(() => {
          this.vipMemberLoading = false;
        });
      } else {
        this.vipMemberOptions = [];
      }
    },
    // 搜索订单
    remoteSearchOrder(query) {
      this.orderLoading = true;
      const searchParams = {
        page: 1,
        pageSize: 20
      };

      // 如果有查询关键词，可能是订单号或手机号
      if (query && query.trim() !== '') {
        const trimmedQuery = query.trim();
        // 判断是否为手机号（11位数字）
        if (/^1[3-9]\d{9}$/.test(trimmedQuery)) {
          searchParams.mobile = trimmedQuery;
        } else {
          // 否则按订单号搜索
          searchParams.orderSn = trimmedQuery;
        }
      }

      // 使用订单查询接口
      getOrderList(searchParams).then(response => {
        this.orderOptions = response.data.paginationResponse.content || [];
        this.orderLoading = false;
      }).catch(() => {
        this.orderOptions = [];
        this.orderLoading = false;
      });
    },
    // 查询邀请人下级数量
    async queryInviterSubCount(userId) {
      try {
        const response = await getCommissionRelationList({
          userId: userId,
          page: 1,
          pageSize: 1 // 只需要获取总数，不需要具体数据
        });

        if (response.data && response.data.dataList) {
          const totalElements = response.data.dataList.totalElements || 0;
          this.$set(this.inviterSubCountMap, userId, totalElements);
          return totalElements;
        }
        return 0;
      } catch (error) {
        console.error('查询邀请人下级数量失败:', error);
        this.$set(this.inviterSubCountMap, userId, 0);
        return 0;
      }
    },
    // 获取邀请人下级数量
    getInviterSubCount(userId) {
      return this.inviterSubCountMap[userId] !== undefined ? this.inviterSubCountMap[userId] : null;
    }
  }
};
</script>
<style lang="scss" scoped>
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 12px;
  height: 12px;
  display: block;
}
::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #bfbfc0;
  border-radius: 5px;
}
::v-deep .el-table--scrollable-y .el-table__body-wrapper {
  overflow: overlay !important;
}
::v-deep .el-table {
  flex: 1;
  overflow: auto;
}
.member-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: calc(100% - 80px);
  margin-left: 80px;
  padding: 10px;
  box-sizing: border-box;
  position: relative;
  padding-bottom: 60px; /* 为固定分页栏留出空间 */

  .search-form {
    border: solid 1px #cccccc;
    margin-top: 0px;
    padding: 15px 10px 0px 10px;
    background: #ffffff;
    margin-bottom: 5px;
    border-radius: 5px;
  }

  .table-container {
    flex: 1;
    overflow: hidden;
    margin-bottom: -20px;
  }

  .pagination {
    position: fixed;
    bottom: 0;
    left: 90px;
    right: 0;
    height: 40px;
    line-height: 40px;
    display: flex;
    justify-content: center;
    background: #ffffff;
    color: #333333;
    border-top: 1px solid #e6e6e6;
    z-index: 999;
    width: calc(100% - 98px);
    padding: 0 10px;
    box-sizing: border-box;
    margin-top: -10px;
    margin-bottom: 5px;
  }

  .input-item {
    width: 240px;
    margin-top: 0px;
  }
}
</style>
<style scoped>
::v-deep .el-pagination.is-background span {
  color: #333;
}
::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: #409EFF;
  color: #fff;
}
::v-deep .el-pagination.is-background .el-pager li {
  background-color: #f4f4f5;
  color: #606266;
}
::v-deep .el-pagination button {
  color: #606266;
}
::v-deep .el-pagination .btn-prev,
::v-deep .el-pagination .btn-next {
  background-color: #f4f4f5;
}
::v-deep .el-select .el-input .el-input__inner {
  color: #606266;
}
::v-deep .el-pagination__jump {
  color: #606266;
}
::v-deep .el-pagination__editor.el-input .el-input__inner {
  color: #606266;
}
</style>
